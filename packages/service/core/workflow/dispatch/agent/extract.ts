import { chats2GPTMessages } from '@fastgpt/global/core/chat/adapt';
import { filterGPTMessageByMaxTokens, formatGPTMessagesInRequestBefore } from '../../../chat/utils';
import type { ChatItemType } from '@fastgpt/global/core/chat/type.d';
import {
  countMessagesTokens,
  countGptMessagesTokens
} from '../../../../common/string/tiktoken/index';
import { ChatItemValueTypeEnum, ChatRoleEnum } from '@fastgpt/global/core/chat/constants';
import { getAIApi, NON_STREAMING_TIMEOUT } from '../../../ai/config';
import type {
  ContextExtractAgentItemType,
  GatewayAppInfo
} from '@fastgpt/global/core/workflow/type/index.d';
import { NodeInputKeyEnum, NodeOutputKeyEnum } from '@fastgpt/global/core/workflow/constants';
import { DispatchNodeResponseKeyEnum } from '@fastgpt/global/core/workflow/runtime/constants';
import type { ModuleDispatchProps } from '@fastgpt/global/core/workflow/type/index.d';
import { Prompt_ExtractJson } from '@fastgpt/global/core/ai/prompt/agent';
import { replaceVariable } from '@fastgpt/global/common/string/tools';
import { LLMModelItemType } from '@fastgpt/global/core/ai/model.d';
import { getHistories } from '../utils';
import { ModelTypeEnum, getRemoteLLMModel } from '../../../ai/model';
import { formatModelChars2Points } from '../../../../support/wallet/usage/utils';
import json5 from 'json5';
import {
  ChatCompletionCreateParams,
  ChatCompletionCreateParamsNonStreaming,
  ChatCompletionMessageParam,
  ChatCompletionTool
} from '@fastgpt/global/core/ai/type';
import { ChatCompletionRequestMessageRoleEnum } from '@fastgpt/global/core/ai/constants';
import { DispatchNodeResultType } from '@fastgpt/global/core/workflow/runtime/type';
import { chatValue2RuntimePrompt } from '@fastgpt/global/core/chat/adapt';
import { createChatCompletion } from '../chatCompletion/chatCompletion';
import { censorLLMMessage } from '../../../../common/censor/reporter';
import { createLogger } from '@fastgpt/global/common/util/logger';
import {
  converse,
  type ConverseParams as ClaudeConverseParams
} from '../../../../common/aws/claude';

type Props = ModuleDispatchProps<{
  [NodeInputKeyEnum.history]?: ChatItemType[];
  [NodeInputKeyEnum.contextExtractInput]: string;
  [NodeInputKeyEnum.extractKeys]: ContextExtractAgentItemType[];
  [NodeInputKeyEnum.description]: string;
  [NodeInputKeyEnum.aiModel]: string;
}>;
type Response = DispatchNodeResultType<{
  [NodeOutputKeyEnum.success]?: boolean;
  [NodeOutputKeyEnum.failed]?: boolean;
  [NodeOutputKeyEnum.contextExtractFields]: string;
}>;

type ActionProps = Props & {
  extractModel: LLMModelItemType;
  gatewayAppInfo: GatewayAppInfo;
  appId: string;
};

const agentFunName = 'request_function';

const logger = createLogger('node-extract');

export async function dispatchContentExtract(props: Props): Promise<Response> {
  const {
    user,
    node: { name },
    histories,
    params: { content, history = 6, model, description, extractKeys },
    gatewayAppInfo
  } = props;

  if (!content) {
    return Promise.reject('没有需要提取的内容');
  }

  const extractModel = await getRemoteLLMModel(model);
  // FIXME: 临时处理，后续需要优化，对于 kwaipilot 模型也用 tool call
  if (extractModel.model.startsWith('kwaipilot')) {
    extractModel.toolChoice = false;
  }

  // const extractModel = getLLMModel(model);
  const chatHistories = getHistories(history, histories);

  const { arg, tokens } = await (async () => {
    if (extractModel.model.startsWith('claude')) {
      logger.info('use aws claude tool choice');
      return awsClaudeToolChoice({
        ...props,
        histories: chatHistories,
        extractModel
      });
    }
    if (extractModel.toolChoice) {
      logger.info('use tool choice');
      return toolChoice({
        ...props,
        histories: chatHistories,
        extractModel
      });
    }
    if (extractModel.functionCall) {
      logger.info('use function call');
      return functionCall({
        ...props,
        histories: chatHistories,
        extractModel
      });
    }
    logger.info('use completions');
    return completions({
      ...props,
      histories: chatHistories,
      extractModel,
      gatewayAppInfo
    });
  })();

  logger.info(`extract result`, { arg });

  // remove invalid key
  for (let key in arg) {
    const item = extractKeys.find((item) => item.key === key);
    if (!item) {
      delete arg[key];
    }
    if (arg[key] === '') {
      delete arg[key];
    }
  }

  // auto fill required fields
  extractKeys.forEach((item) => {
    if (item.required && !arg[item.key]) {
      arg[item.key] = item.defaultValue || '';
    }
  });

  // auth fields
  let success = !extractKeys.find((item) => !(item.key in arg));
  // auth empty value
  if (success) {
    for (const key in arg) {
      const item = extractKeys.find((item) => item.key === key);
      if (!item) {
        success = false;
        break;
      }
    }
  }

  const { totalPoints, modelName } = formatModelChars2Points({
    model: extractModel.model,
    tokens,
    modelType: ModelTypeEnum.llm
  });

  return {
    // [DispatchNodeResponseKeyEnum.skipHandleId]: success
    //   ? [getHandleId(nodeId, 'source', NodeOutputKeyEnum.failed)]
    //   : [getHandleId(nodeId, 'source', NodeOutputKeyEnum.success)],
    [NodeOutputKeyEnum.contextExtractFields]: JSON.stringify(arg),
    ...arg,
    [DispatchNodeResponseKeyEnum.nodeResponse]: {
      totalPoints: totalPoints,
      model: modelName,
      query: content,
      tokens,
      extractDescription: description,
      extractResult: arg,
      contextTotalLen: chatHistories.length + 2
    },
    [DispatchNodeResponseKeyEnum.nodeDispatchUsages]: [
      {
        moduleName: name,
        totalPoints: totalPoints,
        model: modelName,
        tokens
      }
    ]
  };
}

const getFunctionCallSchema = async ({
  extractModel,
  histories,
  params: { content, extractKeys, description }
}: ActionProps) => {
  const messages: ChatItemType[] = [
    ...histories,
    {
      obj: ChatRoleEnum.Human,
      value: [
        {
          type: ChatItemValueTypeEnum.text,
          text: {
            content: `我正在执行一个函数，需要你提供一些参数，请以 JSON 字符串格式返回这些参数，要求：
"""
${description ? `- ${description}` : ''}
- 不是每个参数都是必须生成的，如果没有合适的参数值，不要生成该参数，或返回空字符串。
- 需要结合前面的对话内容，一起生成合适的参数。
"""

本次输入内容: ${content}
            `
          }
        }
      ]
    }
  ];
  logger.info('getFunctionCallSchema messages', { messages });
  const chatMessages = chats2GPTMessages({ messages, reserveId: false });
  logger.info('getFunctionCallSchema chatMessages', { chatMessages });
  const adaptMessages = formatGPTMessagesInRequestBefore(chatMessages);
  logger.info('getFunctionCallSchema adaptMessages', { adaptMessages });
  const filterMessages = await filterGPTMessageByMaxTokens({
    messages: adaptMessages,
    maxTokens: extractModel.maxContext
  });
  logger.info('getFunctionCallSchema filterMessages', { filterMessages });

  const properties: Record<
    string,
    {
      type: string;
      description: string;
    }
  > = {};
  extractKeys.forEach((item) => {
    properties[item.key] = {
      type: 'string',
      description: item.desc,
      ...(item.enum ? { enum: item.enum.split('\n') } : {})
    };
  });
  // function body
  const agentFunction = {
    name: agentFunName,
    description: '需要执行的函数',
    parameters: {
      type: 'object',
      properties,
      required: []
    }
  };

  return {
    filterMessages,
    agentFunction
  };
};

const toolChoice = async (props: ActionProps) => {
  const { user, extractModel, gatewayAppInfo } = props;

  const { filterMessages, agentFunction } = await getFunctionCallSchema(props);

  const tools: ChatCompletionTool[] = [
    {
      type: 'function',
      function: agentFunction
    }
  ];

  const model = extractModel.model;

  const { ai } = await getAIApi({
    model,
    timeout: NON_STREAMING_TIMEOUT
  });

  const requestBody: ChatCompletionCreateParamsNonStreaming = {
    model,
    temperature: 0,
    messages: filterMessages,
    tools,
    tool_choice: { type: 'function', function: { name: agentFunName } }
  };
  const requestedAt = +new Date();
  const response = await ai.chat.completions.create(requestBody);

  const arg: Record<string, any> = (() => {
    try {
      censorLLMMessage({
        userId: user.username,
        appInfo: {
          tokenName: gatewayAppInfo.tokenName,
          tokenOwner: gatewayAppInfo.tokenOwner,
          id: gatewayAppInfo.id
        },
        provider: 'openai',
        model: requestBody.model,
        requestedAt,
        responseStart: +new Date(),
        inputParams: requestBody,
        finalAnswer: '',
        calls: response?.choices?.[0]?.message?.tool_calls ?? []
      });
      return json5.parse(
        response?.choices?.[0]?.message?.tool_calls?.[0]?.function?.arguments || '{}'
      );
    } catch (error) {
      logger.error('tool_choice error', error);
      logger.error('tool_choice agentFunction.parameters', agentFunction.parameters);
      logger.error(
        'tool_choice tool_calls?.[0]?.function',
        response.choices?.[0]?.message?.tool_calls?.[0]?.function
      );
      return {};
    }
  })();

  const completeMessages: ChatCompletionMessageParam[] = [
    ...filterMessages,
    {
      role: ChatCompletionRequestMessageRoleEnum.Assistant,
      tool_calls: response.choices?.[0]?.message?.tool_calls
    }
  ];
  return {
    tokens: await countGptMessagesTokens(completeMessages, tools),
    arg
  };
};

const awsClaudeToolChoice = async (props: ActionProps) => {
  const { user, extractModel, gatewayAppInfo } = props;
  const { filterMessages, agentFunction } = await getFunctionCallSchema(props);
  const tools: ChatCompletionTool[] = [
    {
      type: 'function',
      function: agentFunction
    }
  ];
  const requestBody: ClaudeConverseParams = {
    temperature: 0,
    messages: filterMessages,
    tools,
    tool_choice: { type: 'function', function: { name: agentFunName } }
  };
  const requestedAt = +new Date();
  const response = await converse(requestBody);

  const content = response.output?.message?.content ?? [];
  let arg: Record<string, any> = {};
  let toolId = '';
  for (const item of content) {
    if (item.toolUse?.input) {
      arg = item.toolUse.input as Record<string, any>;
      toolId = item.toolUse.toolUseId ?? '';
      break;
    }
  }

  // 在循环外发送审计信息
  censorLLMMessage({
    userId: user.username,
    appInfo: {
      tokenName: gatewayAppInfo.tokenName,
      tokenOwner: gatewayAppInfo.tokenOwner,
      id: gatewayAppInfo.id
    },
    provider: 'openai',
    model: extractModel.model,
    requestedAt,
    responseStart: +new Date(),
    inputParams: requestBody,
    finalAnswer: '',
    calls: [
      {
        type: 'function',
        id: toolId,
        function: {
          name: agentFunName,
          arguments: JSON.stringify(arg)
        }
      }
    ]
  });

  return {
    tokens: 0,
    arg
  };
};

const functionCall = async (props: ActionProps) => {
  const { user, extractModel, gatewayAppInfo } = props;

  const { agentFunction, filterMessages } = await getFunctionCallSchema(props);
  const functions: ChatCompletionCreateParams.Function[] = [agentFunction];
  const model = extractModel.model;

  const { ai } = await getAIApi({
    model,
    timeout: NON_STREAMING_TIMEOUT
  });

  const requestedAt = +new Date();
  const requestBody = {
    model,
    temperature: 0,
    messages: filterMessages,
    function_call: {
      name: agentFunName
    },
    functions
  };
  const response = await ai.chat.completions.create(requestBody);

  try {
    const functionCall = response?.choices?.[0]?.message?.function_call;
    const arg = JSON.parse(functionCall?.arguments || '');
    const completeMessages: ChatCompletionMessageParam[] = [
      ...filterMessages,
      {
        role: ChatCompletionRequestMessageRoleEnum.Assistant,
        function_call: response.choices?.[0]?.message?.function_call
      }
    ];

    censorLLMMessage({
      userId: user.username,
      appInfo: {
        tokenName: gatewayAppInfo.tokenName,
        tokenOwner: gatewayAppInfo.tokenOwner,
        id: gatewayAppInfo.id
      },
      provider: 'openai',
      model: requestBody.model,
      requestedAt,
      responseStart: +new Date(),
      inputParams: requestBody,
      finalAnswer: '',
      functions: functionCall ? [functionCall] : []
    });

    return {
      arg,
      tokens: await countGptMessagesTokens(completeMessages, undefined, functions)
    };
  } catch (error) {
    logger.error('function_call error', error);

    return {
      arg: {},
      tokens: 0
    };
  }
};

const completions = async ({
  extractModel,
  user,
  histories,
  params: { content, extractKeys, description },
  gatewayAppInfo,
  appId
}: ActionProps) => {
  const messages: ChatItemType[] = [
    {
      obj: ChatRoleEnum.Human,
      value: [
        {
          type: ChatItemValueTypeEnum.text,
          text: {
            content: replaceVariable(extractModel.customExtractPrompt || Prompt_ExtractJson, {
              description,
              json: extractKeys
                .map(
                  (item) =>
                    `{"key":"${item.key}", "description":"${item.desc}"${
                      item.enum ? `, "enum":"[${item.enum.split('\n')}]"` : ''
                    }}`
                )
                .join('\n'),
              text: `${histories.map((item) => `${item.obj}:${chatValue2RuntimePrompt(item.value).text}`).join('\n')}
Human: ${content}`
            })
          }
        }
      ]
    }
  ];

  const answer = await createChatCompletion(
    {
      model: extractModel.model,
      temperature: 0.01,
      messages: chats2GPTMessages({ messages, reserveId: false })
    },
    {
      gatewayAppInfo: gatewayAppInfo,
      username: user.username,
      appId
    }
  );

  // parse response
  const start = answer.indexOf('{');
  const end = answer.lastIndexOf('}');

  if (start === -1 || end === -1) {
    return {
      rawResponse: answer,
      tokens: await countMessagesTokens(messages),
      arg: {}
    };
  }

  const jsonStr = answer
    .substring(start, end + 1)
    .replace(/(\\n|\\)/g, '')
    .replace(/  /g, '');

  try {
    return {
      rawResponse: answer,
      tokens: await countMessagesTokens(messages),
      arg: json5.parse(jsonStr) as Record<string, any>
    };
  } catch (error) {
    logger.error('completion error', error);
    return {
      rawResponse: answer,
      tokens: await countMessagesTokens(messages),
      arg: {}
    };
  }
};
