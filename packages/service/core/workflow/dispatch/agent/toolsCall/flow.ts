import { LLMModelItemType } from '@fastgpt/global/core/ai/model.d';
import { filterGPTMessageByMaxTokens } from '../../../../chat/utils';
import {
  ChatCompletionMessageToolCall,
  ChatCompletionToolMessageParam,
  ChatCompletionMessageParam,
  ChatCompletionTool,
  ChatCompletionAssistantMessageParam,
  ChatCompletionCreateParamsNonStreaming
} from '@fastgpt/global/core/ai/type';
import { responseWrite, responseWriteNodeStatus } from '../../../../../common/response';
import { SseResponseEventEnum } from '@fastgpt/global/core/workflow/runtime/constants';
import { ChatCompletionRequestMessageRoleEnum } from '@fastgpt/global/core/ai/constants';
import { dispatchWorkFlow } from '../../index';
import {
  DispatchToolModuleProps,
  RunToolResponse,
  ToolNodeItemType,
  ToolRunResponseType
} from './type.d';
import json5 from 'json5';
import { countGptMessagesTokens } from '../../../../../common/string/tiktoken/index';
import { GPTMessages2Chats } from '@fastgpt/global/core/chat/adapt';
import { AIChatItemType } from '@fastgpt/global/core/chat/type';
import { getToolDescription, updateRagWebSearchMessages, updateToolInputValue } from './utils';
import { censorLLMMessage } from '../../../../../common/censor/reporter';
import { ChatCompletionCreateParamsStream } from '../../chatCompletion/chatCompletion';
import { DEFAULT_MAX_ITERATION, RAG_WEB_SEARCH_TOOL_ID } from './constants';
import { createLogger } from '@fastgpt/global/common/util/logger';
import { toolsCallKit as toolChoiceToolsCallKit } from './toolChoice';
import { toolsCallKit as kwaipilotToolsCallKit } from './kwaipilotToolChoice';
import { toolsCallKit as promptToolsCallKit } from './promptCall';
import { emitQuoteSource } from '../../utils';

import type { QuoteSource } from '../../type';

const logger = createLogger('node-tools-call-flow');

/*
  调用思路
  1. messages 接收发送给AI的消息
  2. response 记录递归运行结果(累计计算 dispatchFlowResponse, totalTokens和assistantResponses)
  3. 如果运行工具的话，则需要把工具中的结果累计加到dispatchFlowResponse中。 本次消耗的 token 加到 totalTokens, assistantResponses 记录当前工具运行的内容。
*/

export const runToolsCallFlow = async (
  props: DispatchToolModuleProps & {
    messages: ChatCompletionMessageParam[];
    toolNodes: ToolNodeItemType[];
    toolModel: LLMModelItemType;
    maxFilterTokens: number;
  },
  round: number,
  skipRagWebSearch: boolean,
  response?: RunToolResponse
): Promise<RunToolResponse> => {
  const {
    toolModel,
    toolNodes,
    messages,
    emitter,
    runtimeNodes,
    detail = false,
    node,
    stream,
    maxFilterTokens,
    gatewayAppInfo,
    user,
    appId
  } = props;
  const assistantResponses = response?.assistantResponses || [];

  // 原始用户输入内容
  let originalUserInputContent = '';
  for (let i = messages.length - 1; i >= 0; i--) {
    const item = messages[i];
    if (item.role !== ChatCompletionRequestMessageRoleEnum.User) {
      continue;
    }

    if (typeof item.content === 'string') {
      originalUserInputContent = item.content;
    } else {
      originalUserInputContent = item.content.reduce<string>(
        (prev, cur) => (cur.type === 'text' ? prev + cur.text : ''),
        ''
      );
    }
    break;
  }

  const tools: ChatCompletionTool[] = toolNodes
    .filter((item) => !skipRagWebSearch || item.nodeId !== RAG_WEB_SEARCH_TOOL_ID)
    .map((item) => {
      const properties: Record<
        string,
        {
          type: string;
          description: string;
          required?: boolean;
        }
      > = {};
      item.toolParams.forEach((item) => {
        properties[item.key] = {
          type: item.valueType || 'string',
          description: item.toolDescription || ''
        };
      });

      return {
        type: 'function',
        function: {
          name: item.nodeId,
          description: getToolDescription(item),
          parameters: {
            type: 'object',
            properties,
            required: item.toolParams.filter((item) => item.required).map((item) => item.key)
          }
        }
      };
    });

  const model = toolModel.model;
  const toolsCallKit = model.startsWith('kwaipilot')
    ? kwaipilotToolsCallKit
    : model.startsWith('gpt')
      ? toolChoiceToolsCallKit
      : promptToolsCallKit;
  const { formattedMessages, withToolsInPrompt } = toolsCallKit.formatMessagesBeforeSend({
    messages,
    tools,
    round
  });

  const filterMessages = await filterGPTMessageByMaxTokens({
    messages: formattedMessages,
    maxTokens: maxFilterTokens // filter token. not response maxToken
  });
  const formativeMessages = filterMessages.map((item) => {
    if (item.role === 'assistant' && item.tool_calls) {
      return {
        ...item,
        tool_calls: item.tool_calls.map((tool) => ({
          id: tool.id,
          type: tool.type,
          function: tool.function
        }))
      };
    }
    return item;
  });

  /* Run llm */
  const requestedAt = +new Date();
  const requestBody: ChatCompletionCreateParamsNonStreaming | ChatCompletionCreateParamsStream = {
    ...toolModel?.defaultConfig,
    model,
    temperature: 0,
    stream,
    messages: formativeMessages,
    tools,
    tool_choice: 'auto'
  };

  logger.info(`llm request: ${JSON.stringify(requestBody)}`);
  const { answer, toolCalls, calls, originalAnswer } = await toolsCallKit.processToolRequest({
    model,
    requestBody,
    stream,
    toolNodes,
    emitter,
    detail,
    username: user.username,
    gatewayAppInfo,
    appId
  });

  if (!answer && toolCalls.length === 0) {
    return Promise.reject(`ToolCalls LLM api response empty. ${model} ${appId}`);
  }

  if (!model.startsWith('kwaipilot')) {
    censorLLMMessage({
      userId: user.username,
      appInfo: {
        tokenName: gatewayAppInfo.tokenName,
        tokenOwner: gatewayAppInfo.tokenOwner,
        id: gatewayAppInfo.id
      },
      provider: 'openai',
      model: requestBody.model,
      requestedAt,
      responseStart: +new Date(),
      inputParams: requestBody,
      finalAnswer: answer,
      calls
    });
  }

  // Run the selected tool by LLM.
  const toolsRunResponse = (
    await Promise.all(
      toolCalls.map(async (tool) => {
        const toolNode = toolNodes.find((item) => item.nodeId === tool.function?.name);

        if (!toolNode) return;

        const startParams = (() => {
          try {
            return json5.parse(tool.function.arguments);
          } catch (error) {
            return {};
          }
        })();

        // TODO: 给工具传递参数
        const toolRunResponse = await dispatchWorkFlow({
          ...props,
          runtimeNodes: runtimeNodes.map((item) =>
            item.nodeId === toolNode.nodeId
              ? {
                  ...item,
                  isEntry: true,
                  inputs: updateToolInputValue({ params: startParams, inputs: item.inputs })
                }
              : item
          )
        });

        const stringToolResponse = (() => {
          if (typeof toolRunResponse.toolResponses === 'object') {
            return JSON.stringify(toolRunResponse.toolResponses, null, 2);
          }

          return toolRunResponse.toolResponses ? String(toolRunResponse.toolResponses) : 'none';
        })();

        const toolMsgParams: ChatCompletionToolMessageParam = {
          tool_call_id: tool.id,
          role: ChatCompletionRequestMessageRoleEnum.Tool,
          name: tool.function.name,
          content: stringToolResponse
        };

        if (stream && detail) {
          responseWrite({
            write: (t) => emitter.emit('data', t),
            event: SseResponseEventEnum.toolResponse,
            data: JSON.stringify({
              tool: {
                id: tool.id,
                toolName: '',
                toolAvatar: '',
                params: '',
                response: stringToolResponse
              }
            })
          });
        }

        return {
          toolRunResponse,
          toolMsgParams,
          toolResponsePrompt: stringToolResponse
        };
      })
    )
  ).filter(Boolean) as ToolRunResponseType;

  const flatToolsResponseData = toolsRunResponse.map((item) => item.toolRunResponse).flat();
  if (toolCalls.length > 0 && !emitter.closed) {
    const tokens = await countGptMessagesTokens(filterMessages, withToolsInPrompt ? [] : tools);

    const toolsRunResponseForMessages: ToolRunResponseType = [];
    const toolCallsForMessages: ChatCompletionMessageToolCall[] = [];
    let ragSearchEngineResponse = '';
    toolsRunResponse.forEach((item, i) => {
      const toolCallItem = toolCalls[i];
      if (!toolCallItem || !item) {
        return;
      }

      if (item.toolMsgParams.name === RAG_WEB_SEARCH_TOOL_ID) {
        ragSearchEngineResponse = item.toolMsgParams.content || '';
        return;
      }

      toolsRunResponseForMessages.push(item);
      toolCallsForMessages.push(toolCallItem);
    });

    const concatMessages = await toolsCallKit.concatCompleteMessages({
      userInputContent: originalUserInputContent,
      messages: filterMessages,
      toolCalls: toolCallsForMessages,
      toolsRunResponse: toolsRunResponseForMessages,
      originalAnswer,
      answer
    });

    let completeMessages = [...concatMessages];
    if (ragSearchEngineResponse) {
      const updatedMessages = await updateRagWebSearchMessages(
        [...concatMessages],
        ragSearchEngineResponse,
        maxFilterTokens
      );
      completeMessages = updatedMessages.messages;

      if (stream && detail) {
        const searchResult = updatedMessages.searchResult;
        const quoteSource: Extract<QuoteSource, { type: 'web' }>[] = searchResult.map((i) => ({
          type: 'web',
          content: i.content,
          link: i.link,
          title: i.title,
          date: i.date,
          favicon: i.favicon,
          prevContent: i.prevContent,
          nextContent: i.nextContent
        }));
        emitQuoteSource(emitter, quoteSource);
      }
    }

    if (stream && detail) {
      responseWriteNodeStatus({
        write: (t) => emitter.emit('data', t),
        name: node.name
      });
    }

    // tool assistant
    const toolAssistants = toolsRunResponse
      .map((item) => {
        const assistantResponses = item.toolRunResponse.assistantResponses || [];
        return assistantResponses;
      })
      .flat();

    // tool node assistant
    const adaptChatMessages = GPTMessages2Chats(completeMessages);
    const toolNodeAssistant = adaptChatMessages.pop() as AIChatItemType | undefined;

    const toolNodeAssistants = [
      ...assistantResponses,
      ...toolAssistants,
      ...(toolNodeAssistant?.value || [])
    ];

    // concat tool responses
    const dispatchFlowResponse = response
      ? response.dispatchFlowResponse.concat(flatToolsResponseData)
      : flatToolsResponseData;

    /* check stop signal */
    const hasStopSignal = flatToolsResponseData.some(
      (item) => !!item.flowResponses?.find((item) => item.toolStop)
    );
    if (hasStopSignal || round >= DEFAULT_MAX_ITERATION) {
      return {
        dispatchFlowResponse,
        totalTokens: response?.totalTokens ? response.totalTokens + tokens : tokens,
        completeMessages,
        assistantResponses: toolNodeAssistants
      };
    }

    return runToolsCallFlow(
      {
        ...props,
        messages: completeMessages
      },
      round + 1,
      !!ragSearchEngineResponse,
      {
        dispatchFlowResponse,
        totalTokens: response?.totalTokens ? response.totalTokens + tokens : tokens,
        assistantResponses: toolNodeAssistants
      }
    );
  } else {
    // No tool is invoked, indicating that the process is over
    const gptAssistantResponse: ChatCompletionAssistantMessageParam = {
      role: ChatCompletionRequestMessageRoleEnum.Assistant,
      content: answer
    };
    const completeMessages = filterMessages.concat(gptAssistantResponse);
    const tokens = await countGptMessagesTokens(completeMessages, withToolsInPrompt ? [] : tools);
    // console.log(tokens, 'response token');

    // concat tool assistant
    const toolNodeAssistant = GPTMessages2Chats([gptAssistantResponse])[0] as
      | AIChatItemType
      | undefined;

    return {
      dispatchFlowResponse: response?.dispatchFlowResponse || [],
      totalTokens: response?.totalTokens ? response.totalTokens + tokens : tokens,
      completeMessages,
      assistantResponses: [...assistantResponses, ...(toolNodeAssistant?.value || [])]
    };
  }
};
