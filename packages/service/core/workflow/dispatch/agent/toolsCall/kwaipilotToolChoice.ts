import {
  ChatCompletionMessageToolCall,
  ChatCompletionMessageParam
} from '@fastgpt/global/core/ai/type';
import { responseWrite } from '../../../../../common/response';
import { SseResponseEventEnum } from '@fastgpt/global/core/workflow/runtime/constants';
import { textAdaptGptResponse } from '@fastgpt/global/core/workflow/runtime/utils';
import { ChatCompletionRequestMessageRoleEnum } from '@fastgpt/global/core/ai/constants';
import { ToolsCallKit } from './type.d';
import { getToolName } from './utils';
import {
  createKwaipilotStreamChatCompletion,
  KwaipilotToolCall,
  KwaipilotToolChatCompletionChunk
} from '../../chatCompletion/chatCompletion';

const processToolRequest: ToolsCallKit['processToolRequest'] = async ({
  stream,
  detail,
  requestBody,
  emitter,
  toolNodes,
  gatewayAppInfo,
  username,
  appId
}) => {
  const genId = (funcName: string) => funcName;

  const readable = await createKwaipilotStreamChatCompletion(
    {
      ...requestBody,
      stream
    },
    {
      gatewayAppInfo,
      username,
      appId
    }
  );

  if (!stream) {
    const result = readable as KwaipilotToolChatCompletionChunk[];
    const calls: ChatCompletionMessageToolCall[] = result.reduce<ChatCompletionMessageToolCall[]>(
      (prev, item) => [
        ...prev,
        ...(item.tool_calls || []).map<ChatCompletionMessageToolCall>((t) => ({
          ...t,
          id: genId(t.function?.name ?? ''),
          type: 'function'
        }))
      ],
      []
    );

    // 加上name和avatar
    const toolCalls: ChatCompletionMessageToolCall[] = calls.map((tool) => {
      const toolNode = toolNodes.find((item) => item.nodeId === tool.function?.name);
      const id = genId(tool.function?.name ?? '');
      return {
        ...tool,
        id,
        type: 'function',
        toolName: toolNode?.name || '',
        toolAvatar: toolNode?.avatar || ''
      };
    });

    const answer = result[0]?.content || '';
    return {
      answer,
      originalAnswer: answer,
      toolCalls: toolCalls,
      calls
    };
  }

  const write = (t: string) => emitter.emit('data', t);

  let textAnswer = '';
  let toolCalls: ChatCompletionMessageToolCall[] = [];
  let originalToolCalls: KwaipilotToolChatCompletionChunk['tool_calls'] = [];
  const calls: ChatCompletionMessageToolCall[] = [];
  const result = readable as ReadableStream<KwaipilotToolChatCompletionChunk[]>;
  const reader = result.getReader();

  while (true) {
    if (emitter.closed) {
      result.cancel();
      break;
    }

    const ret = await reader.read();
    if (ret.done) {
      break;
    }

    const contentChunk = ret.value[0];
    const toolChunks = [...ret.value];

    if (contentChunk?.content) {
      const content = contentChunk?.content || '';
      textAnswer += content;

      responseWrite({
        write,
        event: detail ? SseResponseEventEnum.answer : undefined,
        data: textAdaptGptResponse({
          text: content
        })
      });
    }

    originalToolCalls = toolChunks.reduce<KwaipilotToolCall[]>(
      (prev, tool) => [...prev, ...(tool?.tool_calls ?? [])],
      []
    );
  }

  originalToolCalls.forEach((toolCall) => {
    const id = genId(toolCall.function?.name ?? '');
    calls.push({
      ...toolCall,
      id,
      type: 'function'
    });
    const toolNode = toolNodes.find((item) => item.nodeId === toolCall.function?.name);

    if (toolNode) {
      const arg: string = toolCall.function?.arguments ?? '';

      toolCalls.push({
        function: {
          name: toolCall.function.name,
          arguments: arg
        },
        type: 'function',
        id,
        toolName: toolNode.name,
        toolAvatar: toolNode.avatar
      });

      if (detail) {
        responseWrite({
          write,
          event: SseResponseEventEnum.toolCall,
          data: JSON.stringify({
            tool: {
              id,
              toolName: getToolName(toolNode),
              toolAvatar: toolNode.avatar,
              functionName: toolCall.function.name,
              params: toolCall.function.arguments ?? '',
              response: ''
            }
          })
        });

        responseWrite({
          write,
          event: SseResponseEventEnum.toolParams,
          data: JSON.stringify({
            tool: {
              id,
              toolName: '',
              toolAvatar: '',
              params: arg,
              response: ''
            }
          })
        });
      }
    }
  });

  return {
    answer: textAnswer,
    originalAnswer: textAnswer,
    toolCalls,
    calls
  };
};

const concatCompleteMessages: ToolsCallKit['concatCompleteMessages'] = ({
  messages,
  toolCalls,
  toolsRunResponse,
  answer
}) => {
  let concatToolMessages = [...messages] as ChatCompletionMessageParam[];
  if (answer) {
    concatToolMessages.push({
      role: ChatCompletionRequestMessageRoleEnum.Assistant,
      content: answer
    });
  }

  const toolMessages = toolsRunResponse.reduce<ChatCompletionMessageParam[]>((prev, item, i) => {
    const toolCallItem = toolCalls[i];
    if (!toolCallItem || !item) {
      return prev;
    }

    const functionName = toolCallItem.function.name;
    return [
      ...prev,
      {
        role: ChatCompletionRequestMessageRoleEnum.Assistant,
        content: '',
        function_call: {
          name: functionName,
          arguments: toolCallItem.function.arguments
        }
      },
      {
        role: ChatCompletionRequestMessageRoleEnum.Function,
        name: functionName,
        content: item.toolMsgParams.content || ''
      }
    ];
  }, []);

  return [...concatToolMessages, ...toolMessages];
};

export const toolsCallKit: ToolsCallKit = {
  processToolRequest,
  concatCompleteMessages,
  formatMessagesBeforeSend: ({ messages }) => {
    return {
      formattedMessages: messages,
      withToolsInPrompt: false
    };
  }
};
