import type { ChatCompletionMessageParam } from '@fastgpt/global/core/ai/type';
import type { NodeInputKeyEnum, NodeOutputKeyEnum } from '@fastgpt/global/core/workflow/constants';
import type { ModuleDispatchProps } from '@fastgpt/global/core/workflow/type/index.d';
import type { RuntimeNodeItemType } from '@fastgpt/global/core/workflow/runtime/type';
import type { ChatNodeUsageType } from '@fastgpt/global/support/wallet/bill/type';
import type { DispatchFlowResponse } from '../../type.d';
import type {
  AIChatItemValueItemType,
  ChatItemValueItemType
} from '@fastgpt/global/core/chat/type';
import type { ChatItemType } from '@fastgpt/global/core/chat/type.d';
import type {
  ChatCompletionMessageToolCall,
  ChatCompletionToolMessageParam,
  ChatCompletionMessageParam,
  ChatCompletionTool,
  ChatCompletionAssistantMessageParam,
  ChatCompletionCreateParamsNonStreaming
} from '@fastgpt/global/core/ai/type';
import type { ChatCompletionCreateParamsStream } from '../../chatCompletion/chatCompletion';
import type { DispatchEvenEmitter, GatewayAppInfo } from '@fastgpt/global/core/workflow/type';

export type DispatchToolModuleProps = ModuleDispatchProps<{
  [NodeInputKeyEnum.history]?: ChatItemType[];
  [NodeInputKeyEnum.aiModel]: string;
  [NodeInputKeyEnum.aiSystemPrompt]: string;
  [NodeInputKeyEnum.userChatInput]: string;
  [NodeInputKeyEnum.aiChatDatasetQuote]?: unknown;
  [NodeInputKeyEnum.aiChatQuoteTemplate]?: string;
  [NodeInputKeyEnum.aiChatQuotePrompt]?: string;
}>;

export type RunToolResponse = {
  dispatchFlowResponse: DispatchFlowResponse[];
  totalTokens: number;
  completeMessages?: ChatCompletionMessageParam[];
  assistantResponses?: AIChatItemValueItemType[];
};
export type ToolNodeItemType = RuntimeNodeItemType & {
  toolParams: RuntimeNodeItemType['inputs'];
};

export type ToolRunResponseType = {
  toolRunResponse: DispatchFlowResponse;
  toolMsgParams: ChatCompletionToolMessageParam;
  toolResponsePrompt: string;
}[];

export interface ToolsCallKit {
  processToolRequest(params: {
    model: string;
    requestBody: ChatCompletionCreateParamsNonStreaming | ChatCompletionCreateParamsStream;
    stream: boolean;
    toolNodes: ToolNodeItemType[];
    emitter: DispatchEvenEmitter;
    detail: boolean;
    username: string;
    gatewayAppInfo: GatewayAppInfo;
    appId: string;
  }): Promise<{
    answer: string;
    originalAnswer: string;
    toolCalls: ChatCompletionMessageToolCall[];
    calls: ChatCompletionMessageToolCall[];
  }>;

  concatCompleteMessages(params: {
    userInputContent: string;
    messages: ChatCompletionMessageParam[];
    toolCalls: ChatCompletionMessageToolCall[];
    toolsRunResponse: ToolRunResponseType;
    answer: string;
    originalAnswer: string;
  }): ChatCompletionMessageParam[];

  formatMessagesBeforeSend(params: {
    messages: ChatCompletionMessageParam[];
    tools: ChatCompletionTool[];
    round: number;
  }): {
    formattedMessages: ChatCompletionMessageParam[];
    // 是否在 prompt 中包含工具的信息
    withToolsInPrompt: boolean;
  };
}
