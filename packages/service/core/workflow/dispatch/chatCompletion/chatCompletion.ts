import { ChatCompletion, ChatCompletionChunk } from 'openai/resources';
import { Stream } from 'openai/streaming';
import { getAIApi, NON_STREAMING_TIMEOUT } from '../../../ai/config';
import { ChatCompletionCreateParamsBase as OpenaiChatCompletionCreateParamsBase } from 'openai/resources/chat/completions';
import { CensorData, censorLLMMessage } from '../../../../common/censor/reporter';
import { GatewayAppInfo } from '@fastgpt/global/core/workflow/type';
import {
  getLLMModelConsumer,
  KwaipilotLlmRequest,
  KwaipilotLlmResponse
} from '../../../../common/rpc/llmKwaipilotModel/index';
import {
  createLogger,
  coreServicePerf,
  type CoreServiceSubTag,
  perLogger
} from '@fastgpt/global/common/util/logger';
import { getGatewayAppLLMCluster, getKwaipilotRetryConfig } from '../../../../common/kconf';
import { converse, converseStream } from '../../../../common/aws/claude';
import { KError } from '@fastgpt/global/common/error/kwaipilotError';
export type ChatCompletionCreateParamsBase = Omit<OpenaiChatCompletionCreateParamsBase, 'stream'>;

export type KwaipilotToolCall = {
  type: string;
  function: {
    arguments: string;
    name: string;
  };
};

export type KwaipilotToolChatCompletionChunk = {
  content: string | null;
  role: string;
  tool_calls?: Array<KwaipilotToolCall>;
};

export type LLMCensorData = {
  username: string;
  appId: string;
  gatewayAppInfo: GatewayAppInfo;
};

export type ChatCompletionCreateParamsStream = ChatCompletionCreateParamsBase & {
  stream: true;
};

export type ChatCompletionFn = {
  (params: ChatCompletionCreateParamsBase, censorData: LLMCensorData): Promise<string>;

  (
    params: ChatCompletionCreateParamsStream,
    censorData: LLMCensorData
  ): Promise<ReadableStream<string>>;

  (
    params: ChatCompletionCreateParamsBase & { stream: boolean },
    censorData: LLMCensorData
  ): Promise<ReadableStream<string> | string>;
};

export type ToolChatCompletionFn = {
  (
    params: ChatCompletionCreateParamsBase,
    censorData: LLMCensorData
  ): Promise<KwaipilotToolChatCompletionChunk[]>;

  (
    params: ChatCompletionCreateParamsStream,
    censorData: LLMCensorData
  ): Promise<ReadableStream<KwaipilotToolChatCompletionChunk[]>>;

  (
    params: ChatCompletionCreateParamsBase & { stream: boolean },
    censorData: LLMCensorData
  ): Promise<
    ReadableStream<KwaipilotToolChatCompletionChunk[]> | KwaipilotToolChatCompletionChunk[]
  >;
};

const ErrorMsg = {
  DeepseekMaxContentLen: 'tokens 超出限制',
  MaxContentLen: 'The input messages exceed the maximum context length',
  ClaudeMaxContentLen: 'Input is too long for requested model',
  MissingUserMsg: 'excluding the system message) must start with a user message ',
  OpenaiMaxContentLen: 'string too long. Expected a string with maximum',
  OpenaiContentPolicy: "Azure OpenAI's content management policy",
  OpenaiTimeout: '408 The operation was timeout'
} as const;

const logger = createLogger('chat-completion-common');
const contentLogger = createLogger('chat-completion-common-content');
const MAX_RETRIES = 1;

const createOpenaiChatCompletion: ChatCompletionFn = (async (
  {
    messages,
    temperature,
    max_tokens,
    stream,
    model,
    ...others
  }: ChatCompletionCreateParamsBase & {
    stream?: boolean;
  },
  censorData
) => {
  const requestedAt = +new Date();
  const perf = (ok: boolean) =>
    coreServicePerf({ subtag: 'chat-gpt4o', ok, startTime: requestedAt });
  const requestBody = {
    model,
    temperature,
    messages,
    max_tokens,
    stream,
    ...others
  };
  const reportCensorData: Omit<CensorData, 'finalAnswer'> = {
    userId: censorData.username,
    appInfo: {
      tokenName: censorData.gatewayAppInfo.tokenName,
      tokenOwner: censorData.gatewayAppInfo.tokenOwner,
      id: censorData.gatewayAppInfo.id
    },
    provider: 'openai',
    model,
    requestedAt,
    responseStart: +new Date(),
    inputParams: requestBody
  };
  const { ai, config } = await getAIApi({
    model,
    timeout: NON_STREAMING_TIMEOUT
  });
  logger.info(`openai stream request: ${JSON.stringify(requestBody)}`);
  let data: Stream<ChatCompletionChunk> | ChatCompletion;
  try {
    data = await ai.chat.completions.create(requestBody);
  } catch (err) {
    logger.error(`openai chat completion error ${err}`, { error: err });
    perf(false);
    // 违反内容管理策略
    if (String(err).indexOf(ErrorMsg.OpenaiContentPolicy) > -1) {
      // 观测由于违法内容安全策略导致的异常
      perLogger.perf({
        subtag: 'openai-model-content-policy',
        extra1: String(censorData.gatewayAppInfo?.id ?? ''),
        extra2: censorData.appId
      });
      throw new KError('GPT_CONTENT_ERROR', 'observation', {
        error: err
      });
    } else if (String(err).indexOf(ErrorMsg.OpenaiTimeout) > -1) {
      // 观测由于超时导致的异常
      perLogger.perf({
        subtag: 'openai-timeout',
        extra1: config.baseURL ?? '',
        extra2: String(censorData.gatewayAppInfo?.id ?? ''),
        extra3: censorData.appId
      });
      throw new KError('OPENAI_REQUEST_TIMEOUT', 'modelError', {
        error: err
      });
    } else if (String(err).indexOf(ErrorMsg.OpenaiMaxContentLen) > -1) {
      throw new KError('CONTEXT_EXCEED_ERROR', 'modelError', {
        error: err
      });
    } else {
      throw new KError('OPENAI_REQUEST_ERROR', 'modelError', {
        error: err
      });
    }
  }

  if (!stream) {
    const finalAnswer = (data as ChatCompletion).choices?.[0]?.message.content || '';
    perf(true);
    censorLLMMessage({ ...reportCensorData, finalAnswer });
    return finalAnswer;
  }

  const { writable, readable } = new TransformStream<ChatCompletionChunk, string>({
    transform(chunk, controller) {
      contentLogger.info(chunk);
      controller.enqueue(chunk.choices?.[0]?.delta?.content || '');
    }
  });

  const streamData = data as Stream<ChatCompletionChunk>;

  (async () => {
    let finalAnswer = '';
    const writer = writable.getWriter();
    try {
      for await (const chunk of streamData) {
        await writer.write(chunk);
        finalAnswer += chunk?.choices?.[0]?.delta?.content || '';
      }
      perf(true);
    } catch (err) {
      perf(false);
      logger.error(`openai chat completion stream error ${err}`, { error: err, streamData });
      const finalError = new KError('OPENAI_REQUEST_ERROR', 'modelError', {
        error: err
      });
      writer.abort(finalError);
    } finally {
      censorLLMMessage({ ...reportCensorData, finalAnswer });
      await writer.close();
    }
  })();

  return readable;
}) as ChatCompletionFn;

const createClaudeChatCompletion: ChatCompletionFn = (async (
  {
    messages,
    temperature,
    max_tokens,
    stream = false,
    model,
    ...others
  }: ChatCompletionCreateParamsBase & {
    stream?: boolean;
  },
  censorData
) => {
  const requestedAt = +new Date();
  const perf = (ok: boolean) =>
    coreServicePerf({ subtag: 'chat-claude', ok, startTime: requestedAt });
  const requestBody = {
    model,
    temperature,
    messages,
    max_tokens,
    stream,
    ...others
  };
  const reportCensorData: Omit<CensorData, 'finalAnswer'> = {
    userId: censorData.username,
    appInfo: {
      tokenName: censorData.gatewayAppInfo.tokenName,
      tokenOwner: censorData.gatewayAppInfo.tokenOwner,
      id: censorData.gatewayAppInfo.id
    },
    provider: 'openai',
    model,
    requestedAt,
    responseStart: +new Date(),
    inputParams: requestBody
  };
  if (!stream) {
    try {
      const response = await converse({
        messages,
        temperature,
        max_tokens
      });
      const finalAnswer = response.output?.message?.content?.[0]?.text ?? '';
      perf(true);
      censorLLMMessage({ ...reportCensorData, finalAnswer });
      return finalAnswer;
    } catch (err) {
      perf(false);
      logger.error(`claude chat completion error ${err}`, { error: err });
      throw new KError('CLAUDE_REQUEST_ERROR', 'modelError', {
        error: err
      });
    }
  }

  let response: Awaited<ReturnType<typeof converseStream>> | undefined;
  try {
    response = await converseStream({
      messages,
      temperature,
      max_tokens
    });
  } catch (err) {
    perf(false);
    logger.error(`claude chat completion stream error ${err}`, { error: err });
    throw new KError('CLAUDE_REQUEST_ERROR', 'modelError', {
      error: err
    });
  }

  const { writable, readable } = new TransformStream<string, string>({
    transform(chunk, controller) {
      contentLogger.info(chunk);
      controller.enqueue(chunk);
    }
  });

  const streamData = response.stream;
  if (!streamData) {
    throw new Error('claude stream is undefined');
  }

  (async () => {
    let finalAnswer = '';
    const writer = writable.getWriter();
    try {
      for await (const chunk of streamData) {
        const delta = chunk.contentBlockDelta?.delta?.text;
        if (delta) {
          await writer.write(delta);
          finalAnswer += delta;
        }
      }
      perf(true);
    } catch (err) {
      perf(false);
      let finalError = err;
      logger.error(`claude stream request error ${err}`, { error: err });
      if (String(err).indexOf(ErrorMsg.ClaudeMaxContentLen) > -1) {
        finalError = new KError('CONTEXT_EXCEED_ERROR', 'modelError', {
          error: err
        });
      } else {
        finalError = new KError('CLAUDE_REQUEST_ERROR', 'modelError', {
          error: err
        });
      }
      writer.abort(finalError);
    } finally {
      censorLLMMessage({ ...reportCensorData, finalAnswer });
      await writer.close();
    }
  })();
  return readable;
}) as ChatCompletionFn;

const createKwaipilotChatCompletion: ChatCompletionFn = (async (
  {
    messages,
    temperature,
    max_tokens,
    stream = false,
    model,
    ...others
  }: ChatCompletionCreateParamsBase & {
    stream?: boolean;
  },
  censorData
) => {
  const subtag: CoreServiceSubTag =
    model === 'kwaipilot_pro_32k'
      ? 'chat-kwaipilot-32k'
      : model === 'kwaipilot_turbo_128k'
        ? 'chat-kwaipilot-128k'
        : 'chat-kwaipilot-r1-math';
  const requestedAt = +new Date();
  const perf = (ok: boolean) =>
    coreServicePerf({
      subtag,
      ok,
      startTime: requestedAt
    });

  const gatewayAppInfo = censorData.gatewayAppInfo;
  const appId = censorData.appId;
  const { cluster, fromGateway } = await getGatewayAppLLMCluster(gatewayAppInfo);
  const consumer = await getLLMModelConsumer(model, cluster, fromGateway, appId);
  const isCommonCluster = cluster === 'common';
  let lastContent = '';
  // 重试次数
  let retryCount = 0;

  const { readable, writable } = new TransformStream<KwaipilotLlmResponse, string>({
    transform(chunk, controller) {
      const responseJsonStr = chunk.getResponseJsonStr();
      contentLogger.info(`kwaipilot chunk: ${responseJsonStr}`);
      if (!responseJsonStr) {
        return;
      }

      let res: Array<KwaipilotToolChatCompletionChunk> = [];
      try {
        res = JSON.parse(responseJsonStr);
      } catch (e: unknown) {
        contentLogger.error(`parse kwaipilot responseJsonStr error ${e}`, { error: e });
        return;
      }

      if (res.length === 0) {
        contentLogger.info(`kwaipilot stream delta length is 0`);
        return;
      }

      const content = res[0].content || '';
      controller.enqueue(content.slice(lastContent.length));
      lastContent = content;
    }
  });
  const writer = writable.getWriter();

  const tryRequest = async (): Promise<void> => {
    const r = new KwaipilotLlmRequest();
    const bodyJsonStr = JSON.stringify({
      messages,
      temperature,
      max_tokens
    });
    logger.info(`kwaipilot stream request: ${bodyJsonStr}`);
    r.setRequestBodyJsonStr(bodyJsonStr);

    const rpcResponse = consumer.getService('KwaipilotLlmApiService').llmChat(r);

    try {
      for await (const chunk of rpcResponse) {
        await writer.write(chunk);
      }
      if (isCommonCluster) {
        perf(true);
      }
      // 说明重试成功了
      if (retryCount > 0) {
        perLogger.perf({
          subtag: 'kwaipilot-retry',
          extra1: 'success',
          extra2: model,
          extra3: appId
        });
      }
    } catch (err) {
      logger.error(`kwaipilot chat completion stream error: ${err}, request: ${bodyJsonStr}`, {
        error: err
      });
      let finalError = err;
      if (isCommonCluster) {
        perf(false);
      }
      if (
        String(err).indexOf(ErrorMsg.MaxContentLen) > -1 ||
        String(err).indexOf(ErrorMsg.DeepseekMaxContentLen) > -1
      ) {
        // 如果是因为 context length 导致的错误，则上报
        perLogger.perf({
          subtag: 'kwaipilot-llm-content-length',
          extra1: String(gatewayAppInfo?.id ?? ''),
          extra2: model,
          extra3: appId
        });
        finalError = new KError('CONTEXT_EXCEED_ERROR', 'modelError', {
          error: err
        });
      } else if (String(err).indexOf(ErrorMsg.MissingUserMsg) > -1) {
        finalError = new KError('MODEL_PARAM_ERROR', 'modelError', {
          error: err,
          request: bodyJsonStr
        });
      } else {
        const isOpen = await getKwaipilotRetryConfig();
        const isFromGatewayApp = Boolean(gatewayAppInfo.id);
        if (isOpen && retryCount < MAX_RETRIES && !lastContent && !isFromGatewayApp) {
          retryCount++;
          logger.info(`kwaipilot retry: ${retryCount}`);
          perLogger.perf({
            subtag: 'kwaipilot-retry',
            extra1: 'retry',
            extra2: model,
            extra3: appId
          });
          // 重试一次
          return await tryRequest();
        }
        if (retryCount > 0) {
          perLogger.perf({
            subtag: 'kwaipilot-retry',
            extra1: 'fail',
            extra2: model,
            extra3: appId
          });
        }
        finalError = new KError('MODEL_32K_OVERFLOW', 'modelError', {
          error: err,
          request: bodyJsonStr
        });
      }
      writer.abort(finalError);
    } finally {
      await writer.close();
    }
  };

  tryRequest();

  if (stream) {
    return readable;
  }

  const reader = readable.getReader();
  let content = '';
  while (true) {
    const result = await reader.read();
    if (result.done) {
      break;
    }
    content += result.value;
  }

  return content;
}) as ChatCompletionFn;

export const createChatCompletion: ChatCompletionFn = (async (
  {
    messages,
    temperature,
    max_tokens,
    stream = false,
    model,
    ...others
  }: ChatCompletionCreateParamsBase & {
    stream?: boolean;
  },
  censorData
) => {
  if (model.startsWith('gpt-4o')) {
    return createOpenaiChatCompletion(
      {
        messages,
        temperature,
        max_tokens,
        stream,
        model,
        ...others
      },
      censorData
    );
  }

  if (model.startsWith('claude')) {
    return createClaudeChatCompletion(
      {
        messages,
        temperature,
        max_tokens,
        stream,
        model,
        ...others
      },
      censorData
    );
  }

  return createKwaipilotChatCompletion(
    {
      messages,
      temperature,
      max_tokens,
      stream,
      model,
      ...others
    },
    censorData
  );
}) as ChatCompletionFn;

export const createKwaipilotStreamChatCompletion: ToolChatCompletionFn = (async (
  {
    messages,
    temperature,
    max_tokens,
    stream,
    model,
    tools
  }: ChatCompletionCreateParamsBase & { stream?: boolean },
  censorData
) => {
  const gatewayAppInfo = censorData.gatewayAppInfo;
  const appId = censorData.appId;
  const { cluster, fromGateway } = await getGatewayAppLLMCluster(gatewayAppInfo);
  const consumer = await getLLMModelConsumer(model, cluster, fromGateway, appId);
  const startTime = +new Date();
  const subtag: CoreServiceSubTag =
    model === 'kwaipilot_pro_32k'
      ? 'chat-kwaipilot-32k'
      : model === 'kwaipilot_turbo_128k'
        ? 'chat-kwaipilot-128k'
        : 'chat-kwaipilot-r1-math';
  const perf = (ok: boolean) => coreServicePerf({ subtag, ok, startTime });
  const isCommonCluster = cluster === 'common';
  let lastContent = '';
  let retryCount = 0;

  const { readable, writable } = new TransformStream<
    KwaipilotLlmResponse,
    KwaipilotToolChatCompletionChunk[]
  >({
    transform(chunk, controller) {
      const responseJsonStr = chunk.getResponseJsonStr();
      contentLogger.info(`kwaipilot stream chunk: ${responseJsonStr}`);
      if (!responseJsonStr) {
        return;
      }

      let delta: Array<KwaipilotToolChatCompletionChunk> = [];
      try {
        delta = JSON.parse(responseJsonStr);
      } catch (e: unknown) {
        contentLogger.error(`parse kwaipilot stream responseJsonStr error ${e}`, { error: e });
        return;
      }

      if (delta.length === 0) {
        contentLogger.info(`kwaipilot stream delta length is 0`);
        return;
      }
      const first = delta[0];
      const content = first.content || '';
      controller.enqueue([
        {
          ...first,
          content: content.slice(lastContent.length)
        },
        ...delta.slice(1)
      ]);
      lastContent = content;
    }
  });
  const writer = writable.getWriter();

  const tryRequest = async (): Promise<void> => {
    const r = new KwaipilotLlmRequest();
    const bodyJsonStr = JSON.stringify({
      messages,
      temperature,
      max_tokens,
      tools
    });
    logger.info(`standalone kwaipilot stream request: ${bodyJsonStr}`);
    r.setRequestBodyJsonStr(bodyJsonStr);

    const rpcResponse = consumer.getService('KwaipilotLlmApiService').llmChat(r);
    try {
      for await (const chunk of rpcResponse) {
        await writer.write(chunk);
      }
      if (isCommonCluster) {
        perf(true);
      }
      // 进行了重试
      if (retryCount > 0) {
        perLogger.perf({
          subtag: 'kwaipilot-retry',
          extra1: 'success',
          extra2: model,
          extra3: appId
        });
      }
    } catch (err) {
      logger.error(`kwaipilot stream iterator error: ${err}, request: ${bodyJsonStr}`);
      let finalError = err;
      if (isCommonCluster) {
        perf(false);
      }

      if (String(err).indexOf(ErrorMsg.MaxContentLen) > -1) {
        // 如果是因为 context length 导致的错误，则上报
        perLogger.perf({
          subtag: 'kwaipilot-llm-content-length',
          extra1: String(gatewayAppInfo?.id ?? ''),
          extra2: model,
          extra3: appId
        });
        finalError = new KError('CONTEXT_EXCEED_ERROR', 'modelError', {
          error: err
        });
      } else if (String(err).indexOf(ErrorMsg.MissingUserMsg) > -1) {
        finalError = new KError('MODEL_PARAM_ERROR', 'modelError', {
          error: err,
          request: bodyJsonStr
        });
      } else {
        const isOpen = await getKwaipilotRetryConfig();
        const isFromGatewayApp = Boolean(gatewayAppInfo.id);
        if (isOpen && retryCount < MAX_RETRIES && !lastContent && !isFromGatewayApp) {
          retryCount++;
          logger.info(`kwaipilot stream retry: ${retryCount}`);
          // 触发重试
          perLogger.perf({
            subtag: 'kwaipilot-retry',
            extra1: 'retry',
            extra2: model,
            extra3: appId
          });
          return await tryRequest();
        }
        // 触发重试，但是失败
        if (retryCount > 0) {
          perLogger.perf({
            subtag: 'kwaipilot-retry',
            extra1: 'fail',
            extra2: model,
            extra3: appId
          });
        }
        finalError = new KError('MODEL_32K_OVERFLOW', 'modelError', {
          error: err,
          request: bodyJsonStr
        });
      }
      writer.abort(finalError);
    } finally {
      await writer.close();
    }
  };

  tryRequest();

  if (stream) {
    return readable;
  }

  const reader = readable.getReader();

  let chunks: KwaipilotToolChatCompletionChunk[] = [];
  let finalAnswer = '';
  while (true) {
    const result = await reader.read();
    if (result.done) {
      break;
    }

    finalAnswer += result.value[0]?.content ?? '';
    chunks = result.value;
  }

  return chunks;
}) as ToolChatCompletionFn;
