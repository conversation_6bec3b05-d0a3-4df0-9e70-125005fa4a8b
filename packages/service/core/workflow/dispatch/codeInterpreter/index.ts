import { NodeInputKeyEnum, NodeOutputKeyEnum } from '@fastgpt/global/core/workflow/constants';
import { ModuleDispatchProps } from '@fastgpt/global/core/workflow/type';
import axios from 'axios';
import { valueTypeFormat } from '../utils';
import { DispatchNodeResultType } from '@fastgpt/global/core/workflow/runtime/type';
import { DispatchNodeResponseKeyEnum } from '@fastgpt/global/core/workflow/runtime/constants';
import { IS_PROD } from '@fastgpt/global/core/chat/constants';
import { installDNSCache } from '@fastgpt/global/common/dns';
import { createLogger } from '@fastgpt/global/common/util/logger';
import { KError } from '@fastgpt/global/common/error/kwaipilotError';

const logger = createLogger('node-code-interpreter');

installDNSCache();
type CodeInterpreterProps = ModuleDispatchProps<
  {
    [NodeInputKeyEnum.addInputParam]: any;
    [NodeInputKeyEnum.textareaInput]: string;
  } & Record<string, any>
>;

type CodeInterpreterResponse = DispatchNodeResultType<{
  [NodeOutputKeyEnum.failed]?: boolean;
}> &
  Record<string, any>;

export const dispatchCodeInterpreter = async (
  props: CodeInterpreterProps
): Promise<CodeInterpreterResponse> => {
  const { params, node } = props;

  const { outputs } = node;

  const { system_addInputParam, system_textareaInput, ...otherInputs } = params;

  const {
    data: { result, error }
  } = await axios.post<{ result: any; error: string }>(
    IS_PROD
      ? 'http://kwaipilot-tool.internal/code/invoke'
      : 'https://code-interceptor.staging.kuaishou.com/code/invoke',
    {
      fn: system_textareaInput,
      args: [otherInputs]
    },
    {
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );

  if (error) {
    logger.error(`code interpreter error: ${error}`, { system_textareaInput, otherInputs });
    throw new KError('CODE_INTERPRETER_ERROR', 'observation', {
      error: error
    });
  }

  if (typeof result !== 'object') {
    throw new Error('返回数据需要是object');
  }

  const results: Record<string, any> = {};

  for (const key in result) {
    const output = outputs.find((item) => item.key === key);
    if (!output) continue;
    results[key] = valueTypeFormat(result[key], output.valueType);
  }

  return {
    [DispatchNodeResponseKeyEnum.nodeResponse]: {
      codeInterpreterResult: result,
      codeInterpreterInput: otherInputs
    },
    ...results
  };
};
