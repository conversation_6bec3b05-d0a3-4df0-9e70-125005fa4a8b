import { DispatchNodeResponseType } from '@fastgpt/global/core/workflow/runtime/type.d';
import { NodeOutputKeyEnum } from '@fastgpt/global/core/workflow/constants';
import {
  DispatchNodeResponseKeyEnum,
  SseResponseEventEnum
} from '@fastgpt/global/core/workflow/runtime/constants';
import { coreServicePerf, createLogger } from '@fastgpt/global/common/util/logger';
import {
  getLLMRagServiceConsumer,
  getMetadata,
  CodeSearchRequest
} from '../../../../common/rpc/llmKwaipilotServiceRag';
import { WarningCode, WarningMap } from '@fastgpt/global/core/chat/warning';
import { responseWrite } from '../../../../common/response';
import { InternalRagSearchType } from '@fastgpt/global/core/dataset/constants';
import { emitQuoteSource } from '../utils';
import { IS_LOCAL } from '@fastgpt/global/core/chat/constants';
import { MOCK_DATA } from './mock';

import type { CodeSearchProps, CodeSearchResponse, CodeSearchResult } from './type';
import type { QuoteSource } from '../type';

const logger = createLogger('node-code-search');

export async function dispatchCodeSearch(props: CodeSearchProps): Promise<CodeSearchResponse> {
  const {
    emitter,
    stream,
    user,
    params: { codeSearch = [], userChatInput, topK = 7, threshold = 0.01 }
  } = props as CodeSearchProps;

  if (!Array.isArray(codeSearch)) {
    return Promise.reject('Quote code type error');
  }

  if (codeSearch.length === 0) {
    return Promise.reject('core.chat.error.Select dataset empty');
  }

  if (!userChatInput) {
    return Promise.reject('core.chat.error.User input empty');
  }

  const startTime = Date.now();

  const result: CodeSearchResult[] = [];
  try {
    const { list, status } = await queryCodeDatasets({
      query: userChatInput,
      repoNames: codeSearch.map((i) => i.repoName),
      commitIds: codeSearch.map((i) => i.commitId),
      targetDirectory: codeSearch.map((i) => i.targetDirectory).flat(),
      username: user.username,
      topK,
      threshold
    });
    if (status === 200) {
      result.push(...list);
    } else if ([411, 412].includes(status) && stream) {
      // 无代码库权限
      responseWrite({
        write: (t) => emitter.emit('data', t),
        event: SseResponseEventEnum.warning,
        data: JSON.stringify(WarningMap[WarningCode.noPermission])
      });
    }
    coreServicePerf({
      subtag: 'rag-code-proxy',
      startTime,
      ok: true
    });
  } catch (e: unknown) {
    logger.error(
      `search ${userChatInput} ${codeSearch.map((i) => i.repoName).join(',')} ${codeSearch.map((i) => i.commitId).join(',')} ${codeSearch.map((i) => i.targetDirectory).join(',')} error: ${e}`,
      { error: e }
    );
    coreServicePerf({
      subtag: 'rag-code-proxy',
      startTime,
      ok: false
    });
  }

  if (stream) {
    const quoteSource: Extract<QuoteSource, { type: 'repo' }>[] = result.map((i) => ({
      ...i,
      type: 'repo'
    }));

    emitQuoteSource(emitter, quoteSource);
  }

  const array = result.map((it) => ({
    __type__: InternalRagSearchType.code,
    ...it
  }));

  if (stream) {
    responseWrite({
      write: (t) => emitter.emit('data', t),
      event: SseResponseEventEnum.datasetCodeSearchResult,
      data: JSON.stringify({
        result: result.map((i) => ({ ...i }))
      })
    });
  }

  const responseData: DispatchNodeResponseType = {
    query: userChatInput,
    textOutput: JSON.stringify(result)
  };

  return {
    [NodeOutputKeyEnum.codeSearchQuotaQA]: array,
    [DispatchNodeResponseKeyEnum.nodeResponse]: responseData
    // [DispatchNodeResponseKeyEnum.toolResponses]: result
  };
}

const queryCodeDatasets = async (data: {
  repoNames: string[];
  commitIds: string[];
  targetDirectory: string[];
  query: string;
  username: string;
  topK: number;
  threshold: number;
}): Promise<{ list: CodeSearchResult[]; status: number }> => {
  if (IS_LOCAL) {
    return MOCK_DATA;
  }
  const consumer = await getLLMRagServiceConsumer();
  const searchService = consumer.getPromisifyService('RagServerService');
  const { repoNames, commitIds, targetDirectory = [], query, username, topK, threshold } = data;

  const metadata = await getMetadata();

  const req = new CodeSearchRequest();
  logger.info(
    `[queryCodeDatasets] ${repoNames.join(',')} ${commitIds.join(',')} ${targetDirectory.join(',')} ${query} ${username}`
  );

  req.setRepoNamesList(repoNames);
  req.setCommitIdsList(commitIds);
  req.setTargetDirectoryList(targetDirectory);
  req.setQuery(query);
  req.setUsername(username);
  req.setTopK(topK);
  req.setThreshold(threshold);

  const res = await searchService.codeSearch(req, metadata);
  logger.info(`[queryCodeDatasets] res ${JSON.stringify(res.toObject())}`, { res: res.toObject() });
  return {
    list: res.getListList().map((it) => {
      const path = it.getPath().replace(/^\/+/, '').replace(/\/+$/, '');
      const startLineNo = it.getStartLineNo();
      const endLineNo = it.getEndLineNo();
      const startColNo = it.getStartColNo();
      const endColNo = it.getEndColNo();
      const language = it.getLanguage();
      const segments = path.split('/');
      const fileName = segments[segments.length - 1] ?? '';
      const index = repoNames.findIndex((i) => i === it.getRepoName());
      const commitId = commitIds[index];
      const formattedRepoName = it.getRepoName().replace(/^\/+/, '').replace(/\/+$/, '');
      const functionName = it.getFunctionName();
      const functionSignature = it.getFunctionSignature();
      const codeType = it.getCodeType();

      return {
        id: String(it.getId()),
        code: it.getCode(),
        repoName: formattedRepoName,
        startLineNo: startLineNo,
        endLineNo: endLineNo,
        startColNo: startColNo,
        endColNo: endColNo,
        language: language,
        path: path,
        fileName: fileName,
        commitId: commitId,
        functionName: functionName,
        functionSignature: functionSignature,
        codeType: codeType
      };
    }),
    status: res.getStatus()
  };
};
