import { CodeDatasetType } from '@fastgpt/global/core/workflow/api';
import { NodeInputKeyEnum, NodeOutputKeyEnum } from '@fastgpt/global/core/workflow/constants';
import { DispatchNodeResultType } from '@fastgpt/global/core/workflow/runtime/type';
import { ModuleDispatchProps } from '@fastgpt/global/core/workflow/type';
import type { DatasetCodeSearchResult } from '@fastgpt/global/core/dataset/type';

export type CodeSearchProps = ModuleDispatchProps<{
  [NodeInputKeyEnum.codeSearchSelectList]: CodeDatasetType;
  [NodeInputKeyEnum.userChatInput]: string;
  [NodeInputKeyEnum.topK]: number;
  [NodeInputKeyEnum.threshold]: number;
}>;

export type CodeSearchResponse = DispatchNodeResultType<{
  [NodeOutputKeyEnum.codeSearchQuotaQA]: Array<DatasetCodeSearchResult>;
}>;

export type CodeSearchResult = {
  id: string;
  code: string;
  startLineNo: number;
  endLineNo: number;
  startColNo: number;
  endColNo: number;
  language: string;
  path: string;
  repoName: string;
  fileName: string;
  commitId: string;
  functionName: string;
  functionSignature: string;
  codeType: string;
};
