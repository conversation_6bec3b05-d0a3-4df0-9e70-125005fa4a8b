import { DispatchNodeResponseType } from '@fastgpt/global/core/workflow/runtime/type.d';
import { NodeOutputKeyEnum } from '@fastgpt/global/core/workflow/constants';
import {
  DispatchNodeResponseKeyEnum,
  SseResponseEventEnum
} from '@fastgpt/global/core/workflow/runtime/constants';
import { coreServicePerf, createLogger } from '@fastgpt/global/common/util/logger';
import {
  getLLMRagServiceConsumer,
  KnowledgeRepoSearchRequest,
  ChatMessage,
  getMetadata,
  KnowledgeRepoSearchResponse
} from '../../../../common/rpc/llmKwaipilotServiceRag';
import { responseWrite } from '../../../../common/response';
import { MOCK_DATASET } from './mock';
import { emitQuoteSource, getPlainTextMessages } from '../utils';
import { getGatewayAppLLMCluster } from '../../../../common/kconf';
import {
  LLM_CLUSTER_DATA_ASSET,
  LLM_CLUSTER_PRIVATE_REDIRECT,
  LLM_CLUSTER_COMMON,
  LLM_CLUSTER_KWAIBI
} from '@fastgpt/global/common/constants';
import { InternalRagSearchType } from '@fastgpt/global/core/dataset/constants';

import type { ChatItemType } from '@fastgpt/global/core/chat/type';
import type { DatasetSearchProps, DatasetSearchResponse, DatasetSearchResult } from './type';
import type { QuoteSource } from '../type';

export type { DatasetSearchResponse } from './type';

const logger = createLogger('node-dataset-search-v2');

export async function dispatchDatasetSearchV2(
  props: DatasetSearchProps
): Promise<DatasetSearchResponse> {
  const {
    emitter,
    stream,
    histories,
    params: { datasetsV2 = [], userChatInput, topK = 7, threshold = 0.01 },
    gatewayAppInfo
  } = props as DatasetSearchProps;

  if (!Array.isArray(datasetsV2)) {
    return Promise.reject('Quote type error');
  }

  if (datasetsV2.length === 0) {
    return Promise.reject('core.chat.error.Select dataset empty');
  }

  if (!userChatInput) {
    return Promise.reject('core.chat.error.User input empty');
  }

  const startTime = Date.now();

  const datasetIds = datasetsV2.map((item) => item.datasetId);

  const result: DatasetSearchResult[] = [];
  const { cluster } = await getGatewayAppLLMCluster(gatewayAppInfo);
  try {
    const res = await queryDatasets(userChatInput, topK, threshold, datasetIds, histories, cluster);
    result.push(...res);
    if (cluster === 'common') {
      coreServicePerf({
        subtag: 'rag-knowledge-proxy',
        startTime,
        ok: true
      });
    }
  } catch (e: unknown) {
    logger.error(`search ${userChatInput} ${datasetIds.join(',')} error: ${e}`, { error: e });
    if (cluster === 'common') {
      coreServicePerf({
        subtag: 'rag-knowledge-proxy',
        startTime,
        ok: false
      });
    }
  }

  if (stream) {
    const quoteSource: Extract<QuoteSource, { type: 'doc' }>[] = result.map((i) => ({
      type: 'doc',
      content: i.text,
      link: i.link,
      title: i.title,
      knowledgeRepoId: i.id,
      knowledgeRepoName: i.knowledgeRepoName
    }));
    emitQuoteSource(emitter, quoteSource);
  }

  const array = result.map((it) => ({
    __type__: InternalRagSearchType.docs,
    ...it
  }));

  if (stream) {
    responseWrite({
      write: (t) => emitter.emit('data', t),
      event: SseResponseEventEnum.datasetSearchResult,
      data: JSON.stringify({
        result: result.map((i) => ({
          id: i.id,
          text: i.text,
          link: i.link,
          tag: i.tag,
          title: i.title
        }))
      })
    });
  }

  const responseData: DispatchNodeResponseType = {
    query: userChatInput,
    textOutput: JSON.stringify(result)
  };

  return {
    [NodeOutputKeyEnum.datasetQuoteQAV2]: array,
    [DispatchNodeResponseKeyEnum.nodeResponse]: responseData
    // [DispatchNodeResponseKeyEnum.toolResponses]: result
  };
}

const queryDatasets = async (
  query: string,
  topK: number,
  threshold: number,
  ids: Array<number>,
  messages: ChatItemType[],
  cluster: string
) => {
  if (process.env.DATASET_LOCAL_MOCK) {
    return MOCK_DATASET;
  }

  const consumer = await getLLMRagServiceConsumer();

  const searchService = consumer.getPromisifyService('RagServerService');

  const metadata = await getMetadata();

  const req = new KnowledgeRepoSearchRequest();
  logger.info(`${ids.join(',')} ${query}`);

  req.setKnowledgeRepoIdsList(ids);
  req.setQuery(query);
  req.setPlatform('KwaipilotAgent');
  req.setTopK(topK);
  req.setThreshold(threshold);
  req.setCluster(cluster);
  logger.info(`knowledge search param: ${JSON.stringify(req.toObject())}`);

  const chatHistories = getPlainTextMessages(messages);
  const results = chatHistories.map((it) => {
    const chatHistory = new ChatMessage();
    chatHistory.setRole(it.role);
    chatHistory.setContent(it.content);
    return chatHistory;
  });

  // 最多使用3对（6条）记录
  req.setChatHistoryList(results.slice(Math.max(results.length - 6, 0)));

  const searchDataset = async (): Promise<KnowledgeRepoSearchResponse | null> => {
    if (cluster === LLM_CLUSTER_COMMON) {
      logger.info(`dataset search knowledgeRepoSearch`);
      return searchService.knowledgeRepoSearch(req, metadata);
    } else {
      logger.info(`dataset search knowledgeRepoSearchForCluster`);
      return searchService.knowledgeRepoSearchForCluster(req, metadata);
    }
  };

  logger.info(`dataset search cluster ${cluster}`);
  const res = await searchDataset();
  if (!res) {
    logger.error(`dataset search cluster ${cluster} not support`);
    return [];
  }
  logger.info('dataset search result', { result: res.toObject() });

  return res.getKnowledgeList().map((it) => ({
    id: it.getKnowledgeRepoId(),
    text: it.getContent(),
    link: it.getLink(),
    title: it.getTitle(),
    tag: it.getTag(),
    knowledgeRepoName: it.getKnowledgeRepoName()
  }));
};
